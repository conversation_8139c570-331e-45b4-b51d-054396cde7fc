class ProjectsController < ApplicationController
  include SecureFileLogging
  
  before_action :authenticate_user!
  before_action :set_project, except: [:show, :index, :new, :create, :show_my, :show_accessible, :show_incoming, :grant_full_details, :deny_access, :delete_access ]
  
  # MEMORY LEAK FIX: Clear geocoder cache after every request to prevent memory accumulation
  after_action :clear_geocoder_cache
  
  # MEMORY LEAK FIX: Clear template cache periodically to prevent ActionView string accumulation  
  after_action :clear_template_cache_periodically


  #Authorization: full_list_for_user solves query to get only those projects he can see
  def index
    # MEMORY LEAK FIX: Cache translations to prevent ActionView string accumulation
    setup_cached_translations
    
    @projects = if current_user
      puts "--------------------------------- starting search"
      base_query = Project.full_list_for_user(current_user).includes(user: :user_profile).active.approved
      
      if params[:project_type].present? && Project.project_types.keys.include?(params[:project_type])
        base_query = base_query.where(project_type: params[:project_type])
      end

      if params[:category].present? && Project.categories.keys.include?(params[:category])
        base_query = base_query.where(category: params[:category])
      end

      if params[:subcategory].present? && Project.subcategories.keys.include?(params[:subcategory])
        base_query = base_query.where(subcategory: params[:subcategory])
      end
      
      if params[:search].present? && params[:search].length <= 100
        search_term = "%#{ActiveRecord::Base.sanitize_sql_like(params[:search])}%"
        base_query = base_query.where("unaccent(summary) ILIKE unaccent(?)", search_term)
      end

      if params[:access].present? && params[:access] != 'all' && ProjectAuth.access_levels.key?(params[:access].to_sym)
        base_query = base_query.where('project_auths.access_level = ?', ProjectAuth.access_levels[params[:access].to_sym])
      end

      if params[:location].present?

        begin
          radius = params[:radius].to_i.clamp(1, 500) if params[:radius].present?
          radius ||= 20

          country_code = params[:search_country_code]
          is_country_search = params[:is_country_search] == 'true'

          if is_country_search && country_code.present?
            base_query = base_query.where(country_code: country_code)
          elsif params[:latitude].present? && params[:longitude].present?
            coordinates = [params[:latitude], params[:longitude]]
            base_query = base_query.unscope(:order).near(coordinates, radius, units: :km)
          else
            base_query = base_query.unscope(:order).near(params[:location], radius, units: :km)
          end
          
        rescue Geocoder::Error => e
          Rails.logger.error("Geocoder error: #{e.message}")
          flash.now[:alert] = "Location search temporarily unavailable. Showing all results."
        ensure
          # MEMORY LEAK FIX: Clear geocoder cache to prevent memory accumulation
          clear_geocoder_cache_safe
        end
      end

      base_query

    else
      Project.summary_visible_public
    end
    
    @pagy, @projects = pagy(@projects, limit: 10)
  end

  # Authorization: only current_users projects
  def show_my
    # Organize projects by status for better UI
    @drafts = current_user.projects.drafts.order(updated_at: :desc)
    @pending = current_user.projects.pending_approval.order(updated_at: :desc)
    @published = current_user.projects.published.includes(:project_auths).order(updated_at: :desc)
    
    # For backward compatibility, keep the original variable structure
    @projects_auths = Project.includes(:project_auths)
                          .where(user_id: current_user.id)
                          .left_joins(:project_auths)
                          .group('projects.id')
                          .select('projects.*, COUNT(CASE WHEN project_auths.access_level = 1 THEN 1 END) as pending_auths_count')
                          .distinct
                          .order(updated_at: :desc)
    render 'index'
  end

  # Authorization: ProjectAuth specific ActionPolicy
  def show
    begin
      @project = Project.find(params[:id])
      unless (@project.user == current_user) || (@project.project_status == true && @project.approved == true)
        redirect_to projects_path, alert: t('projects.common.offer_not_available', default: 'The requested offer is not available.')
      else
        if allowed_to?(:view_full_details?, @project)
          @private_files = @project.private_files
          @auth_requests = @project.connection_requests.where(status: :pending)
          render 'show_full_details'
        else
          render 'show_summary'
        end
      end
    rescue ActiveRecord::RecordNotFound
      redirect_to projects_path, alert: t('projects.common.offer_not_available', default: 'The requested offer is not available.')
    end
  end

  # Authorization: Only project owner can see users pending or approved
  # Double check with view restriction - if not owner, will return empty
  def show_users
    if allowed_to?(:manage_access?, @project)
      @project_auths = @project.project_auths.where(access_level: :full_details)
      @auth_requests = @project.connection_requests.where(status: :pending)

      render 'show_full_details'
    else
      render 'show_full_details'
    end
  end

  # Owner of the project deletes access for another user to his own project
  def delete_access
    @project_auth = ProjectAuth.find(params[:id])
    @project = @project_auth.project
    
    # SECURITY FIX: Use explicit and specific authorization check for clarity and security
    # This ensures only project owners can manage access permissions
    authorize! @project, to: :manage_access?
    
    @project_auth_request = ConnectionRequest.find_by(project_id: @project.id, inviter_id: @project_auth.user_id, invitee_id: current_user.id)
    ActiveRecord::Base.transaction do
      @project_auth.destroy!
      @project_auth_request&.destroy! 
      redirect_to show_users_project_path(@project), notice: 'Access removed.'
    rescue ActiveRecord::RecordNotDestroyed => e
      redirect_to show_users_project_path(@project), alert: 'Failed to remove access. '
    end
  end 

  def new
    # DEFERRED CREATION: Don't create project until user makes first change
    # Build project instance for form but don't save yet
    @project = current_user.projects.build(project_status: false)
    
    # Set defaults for form display (not saved until user makes changes)
    # Model callbacks will set these defaults when project is actually created
    
    # Render form directly - no database interaction yet
    render :new
  end

  # Authorization: Only project owner can see users pending or approved
  def edit
    authorize! @project

    # Moved the connected and pending users into one view
    @project_auths = @project.project_auths.where(access_level: :full_details)
    @auth_requests = @project.connection_requests.where(status: :pending)
    
    # Count active uploads for simple notification (exclude aborted uploads)
    @active_uploads_count = Upload.where(
      target_type: 'Project', 
      target_id: @project.id,
      user: current_user
    ).where.not(status: ['completed', 'failed', 'cancelled', 'aborted']).count
    
    # Get stuck uploads for cleanup button
    @stuck_uploads = Upload.where(
      target_type: 'Project', 
      target_id: @project.id,
      user: current_user,
      status: ['transferred', 'processing']
    ).where('updated_at < ?', 30.minutes.ago)

  end

  def create
    # Handle autosave requests from new project form (first save)
    if params[:autosave] == 'true'
      # Filter out empty string values to prevent spurious creation
      filtered_params = autosave_params.reject { |k, v| v.blank? }
      
      @project = current_user.projects.build(filtered_params)
      @project.project_status = false  # Always create as draft
      
      # Only create if there are actual non-empty changes
      if @project.changed? && filtered_params.present?
        @project.save(validate: false)
        # Return the new project ID for JavaScript
        render json: { project_id: @project.id }
      else
        # No meaningful changes, don't create project
        head :ok
      end
      return
    end
    
    # Regular form submission (non-autosave)
    @project = current_user.projects.build(project_params)
    @project.project_status = false  # Always create as draft
    validate_private_files
    
    if @project.save(validate: false)  # Skip validations for drafts
      
      # Colorful logging for file uploads
      if @project.private_files.attached?
        @project.private_files.each do |file|
          Rails.logger.info "\e[33m[FILE UPLOAD]\e[0m User #{current_user.id} uploaded file #{file.id} (#{file.filename}) to new project #{@project.id} - Size: #{file.blob.byte_size} bytes"
        end
      end
      
      redirect_to edit_project_path(@project), notice: t('projects.create.draft_created', default: 'Draft created. You can now add files and complete the details.')
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    authorize! @project
    
    validate_private_files
    
    # Handle autosave requests
    if params[:autosave] == 'true'
      # Filter out empty string values to prevent spurious saves
      filtered_params = autosave_params.reject { |k, v| v.blank? }
      @project.assign_attributes(filtered_params)
      
      # DEFERRED CREATION: Create project on first autosave if it doesn't exist yet
      if @project.new_record?
        # First autosave - create the project with changes
        if @project.changed? && filtered_params.present?
          @project.save(validate: false)
          # Return the new project ID so JavaScript can update the form action
          render json: { project_id: @project.id }
          return
        else
          # No meaningful changes yet, don't create project
          head :ok
          return
        end
      else
        # Existing project - only save if there are changes (Rails dirty tracking)
        if @project.changed?
          @project.save(validate: false)
        end
        head :ok
        return
      end
    end
    
    # Assign form attributes first
    @project.assign_attributes(project_params)
    
    # Check if user wants to publish (project_status = true from toggle)
    if @project.project_status == true
      Rails.logger.info "[PUBLISH] User #{current_user.id} attempting to publish project #{@project.id}"
      
      # Debug validation state before save
      Rails.logger.info "[PUBLISH] Project #{@project.id} validation state: valid=#{@project.valid?}, errors=#{@project.errors.full_messages}"
      
      if @project.save
        # Send admin notification when publishing a project that needs approval
        if @project.project_status && !@project.approved?
          Rails.logger.info "🔔 [CONTROLLER] Publishing project #{@project.id} requires approval - sending notifications"
          admin_users = User.where(role: 'super_boss').where.not(confirmed_at: nil).where(locked_at: nil)
          Rails.logger.info "🔔 [CONTROLLER] Found #{admin_users.count} admin users to notify"
          admin_users.each do |admin|
            Rails.logger.info "🔔 [CONTROLLER] Queueing notification to #{admin.email}"
            NotificationMailer.admin_project_notification(@project, admin, current_user).deliver_later
          end
          redirect_to edit_project_path(@project), notice: t('projects.update.submitted_for_approval', default: 'Deal submitted for approval.')
        else
          # Project was saved but didn't need approval
          redirect_to edit_project_path(@project), notice: t('projects.common.saved', default: 'Deal saved.')
        end
      else
        # Validation failed, keep as draft
        @project.project_status = false
        @project_auths = @project.project_auths.where(access_level: :full_details)
        @auth_requests = @project.connection_requests.where(status: :pending)
        render :edit, status: :unprocessable_entity
      end
    else
      # Draft mode - skip validations
      @project.save(validate: false)
      
      # Colorful logging for new file uploads during update
      if @project.private_files.attached? && @project.saved_change_to_attribute?(:private_files)
        @project.private_files.each do |file|
          Rails.logger.info "\e[33m[FILE UPLOAD]\e[0m User #{current_user.id} uploaded/updated file #{file.id} (#{file.filename}) to project #{@project.id} - Size: #{file.blob.byte_size} bytes"
        end
      end
      
      # Note: Admin notifications are only sent when publishing (handled in published section above)
      # Draft changes don't trigger notifications to avoid spamming admins

      redirect_to edit_project_path(@project), notice: t('projects.update.draft_saved', default: 'Draft saved.')
    end
  end

  def destroy_file
    authorize! @project
    file = @project.private_files.find(params[:file_id])
    file.purge
    flash[:notice] = t('projects.common.file_deleted', default: "File successfully deleted.")
    redirect_to edit_project_path(@project)
  rescue StandardError => e
    flash[:alert] = "Unable to delete the file: #{e.message}"
    redirect_to edit_project_path(@project)
  end

  def bulk_delete_files
    authorize! @project
    
    file_ids = params[:file_ids]
    
    unless file_ids.present? && file_ids.is_a?(Array)
      render json: { error: 'No files selected' }, status: :bad_request
      return
    end
    
    # Security: scope files to current project to prevent deletion of files from other projects
    files_to_delete = @project.private_files.where(id: file_ids)
    
    if files_to_delete.empty?
      render json: { error: 'No valid files found' }, status: :bad_request
      return
    end
    
    deleted_count = 0
    
    # Delete files in a transaction for atomicity
    ActiveRecord::Base.transaction do
      files_to_delete.each do |file|
        file.purge
        deleted_count += 1
      end
    end
    
    render json: { 
      message: t('projects.common.files_deleted', count: deleted_count, default: "%{count} files deleted successfully"),
      deleted_count: deleted_count 
    }
  rescue StandardError => e
    Rails.logger.error "Bulk file deletion error: #{e.message}"
    render json: { error: 'Failed to delete files' }, status: :internal_server_error
  end

  def destroy
    authorize! @project
    @project.destroy
    redirect_to show_my_projects_path, notice: t('projects.common.project_deleted', default: "Project was deleted."), status: :see_other
  end

  # New action for updating project approval status by an admin
  def update_approval
    # set_project is called via before_action for this :id
    unless current_user.super_admin? || current_user.admin?
      redirect_to root_path, alert: "Not authorized."
      return
    end

    @project.admin_approver = current_user 
    @project.is_admin_approval_action = true

    if @project.update(project_approval_params)
      # Send new project notification if admin approves, project is active, and not yet published
      if @project.approved? && @project.project_status
        # Atomically update the timestamp to prevent race conditions.
        rows_affected = Project.where(id: @project.id, first_published_at: nil)
                               .update_all(first_published_at: Time.current)

        if rows_affected > 0
          users_to_notify = if @project.network_only?
                              @project.user.connected_users_bidirectional.active.includes(:user_profile)
                            elsif @project.semi_public?
                              User.active.includes(:user_profile)
                            else
                              []
                            end

          # Use bulk notification to prevent job queue flooding
          if users_to_notify.any?
            BulkNotificationJob.perform_later(@project, users_to_notify.pluck(:id))
          end
        end
      end
      redirect_to admin_dashboard_path, notice: "Project approval status updated successfully."
    else
      redirect_to admin_dashboard_path, alert: "Failed to update project approval status: #{@project.errors.full_messages.join(', ')}"
    end
  end

  # Generate secure token for file access
  # POST /projects/:id/request_file_token
  def request_file_token
    # CHUNK 10: Add context validation and secure logging
    # Check for rate limiting (Rack::Attack sets this header)
    if request.env['rack.attack.throttle_data']
      log_security_violation('rate_limit_exceeded', { 
        throttle_data: request.env['rack.attack.throttle_data'].keys 
      })
      return render json: { 
        error: 'Rate limit exceeded', 
        retry_after: 60 
      }, status: :too_many_requests
    end
    
    # Authorization - ensure user can view full details of the project
    begin
      authorize! @project, to: :view_full_details?
    rescue ActionPolicy::Unauthorized
      log_secure_access('token_request_unauthorized', @project.id, false)
      return head :forbidden
    end
    
    # Validate required parameter
    file_hash = params[:file_hash]
    unless file_hash.present?
      log_secure_access('token_request_missing_hash', @project.id, false)
      return render json: { error: 'Invalid request - file_hash parameter required' }, status: :bad_request
    end
    
    # Find file by secure hash (not ID) to prevent enumeration
    file_attachment = @project.find_file_by_secure_hash(file_hash)
    
    unless file_attachment
      log_secure_access('token_request_file_not_found', @project.id, false)
      return render json: { error: 'File not found' }, status: :not_found
    end
    
    # Generate token scoped to this user, file, and current session
    token = SecureFileTokenService.generate_token(file_attachment, current_user, request: request)
    
    # Log successful token generation
    log_secure_access('token_request_success', @project.id, true)
    
    render json: { 
      token: token,
      content_type: file_attachment.content_type,
      expires_in: JWT_EXPIRATION.to_i
    }
  rescue StandardError => e
    log_secure_access('token_request_error', @project&.id || 'unknown', false)
    Rails.logger.error "[SECURE_FILE] Token request error: #{e.class.name}"
    render json: { error: 'Internal server error' }, status: :internal_server_error
  end

  private

  def set_project
    # SECURITY: Find project and ensure user has access via ActionPolicy
    # This prevents unauthorized access to projects by ID enumeration
    @project = Project.includes(private_files_attachments: :blob).find(params[:id])
    
    # Note: Authorization is enforced via ActionPolicy in individual actions
    # Each action must call authorize! with appropriate permission check
  rescue ActiveRecord::RecordNotFound
    # Return 404 for non-existent projects to prevent enumeration
    raise ActionController::RoutingError.new('Not Found')
  end

  #public_visible not being used for now
  def project_params
    params.require(:project).permit(
      :summary, 
      :summary_only, 
      :full_access, 
      :location, 
      :visibility, 
      :semi_public, 
      :friends_only, 
      :price_value, 
      :price_currency, 
      :price_text, 
      :full_description, 
      :land_area, 
      :area_unit,
      :network_only,
      :category,
      :subcategory,
      :longitude,
      :latitude,
      :country,
      :country_code,
      :project_type,
      :commission,
      :commission_type,
      :project_status,
      private_files: [],
      remove_private_files: []
    )
  end

  # Restricted parameters for autosave - excludes sensitive state fields
  def autosave_params
    params.require(:project).permit(
      :summary, # INCLUDED for deferred creation flow - needed to trigger first save
      :location, 
      :price_value, 
      :price_currency, 
      :price_text, 
      :full_description, 
      :land_area, 
      :area_unit,
      :category,
      :subcategory,
      :longitude,
      :latitude,
      :country,
      :country_code,
      :project_type,
      :commission,
      :commission_type
      # SECURITY: Excluded sensitive fields that should not be changed via autosave:
      # :project_status, :summary_only, :full_access, :semi_public, :network_only, :visibility, :friends_only
      # File uploads also excluded from autosave for security
    )
  end

  def validate_private_files
    return unless @project.private_files.attached?
  
    newly_attached_files = @project.private_files.attachments.select { |att| !att.persisted? }
  
    newly_attached_files.each do |attachment|
      unless attachment.content_type.in?(['application/pdf', 'image/png', 'image/jpeg', 'image/tiff'])
        @project.errors.add(:private_files, "must be a PDF or image. Invalid file: #{attachment.filename} (#{attachment.content_type})")
      end
    end
  end

  def project_approval_params
    params.require(:project).permit(:approved)
  end

  # MEMORY LEAK FIX: Cache translations to prevent ActionView string accumulation
  def setup_cached_translations
    @cached_project_types = Project::PROJECT_TYPES
    @cached_categories = Project::CATEGORIES
    @cached_translations = Rails.cache.fetch("project_translations_#{I18n.locale}", expires_in: 1.hour) do
      {
        projectTypes: Hash[Project.project_types.keys.map { |t| [t, I18n.t("models.project.project_types.#{t}")] }],
        categories: Hash[Project.categories.keys.map { |c| [c, I18n.t("models.project.categories.#{c}")] }],
        subcategories: Hash[Project.subcategories.keys.map { |s| [s, I18n.t("models.project.subcategories.#{s}")] }]
      }
    end
    @cached_placeholders = Rails.cache.fetch("project_placeholders_#{I18n.locale}", expires_in: 1.hour) do
      {
        category: t("projects.index.filters.category_placeholder"),
        subcategory: t("projects.index.filters.subcategory_placeholder")
      }
    end
  end

  # MEMORY LEAK FIX: Clear template cache periodically to prevent ActionView string accumulation
  def clear_template_cache_periodically
    # Clear template cache on 10% of requests to prevent memory buildup
    if Rails.env.production? && rand < 0.1
      begin
        # Clear ActionView template cache
        ActionView::LookupContext::DetailsKey.clear if defined?(ActionView::LookupContext::DetailsKey)
        
        # Clear I18n cache
        I18n.backend.reload! if I18n.backend.respond_to?(:reload!)
        
        Rails.logger.info "Template cache cleared to prevent memory leak"
      rescue => e
        Rails.logger.warn "Failed to clear template cache: #{e.message}"
      end
    end
  end

  # MEMORY LEAK FIX: Clear geocoder cache to prevent memory accumulation
  def clear_geocoder_cache
    clear_geocoder_cache_safe
  end

  def clear_geocoder_cache_safe
    begin
      # Clear the geocoder cache using the official geocoder gem method
      if defined?(Geocoder)
        # Try the official cache expiration method first
        if Geocoder::Lookup.respond_to?(:get) && Geocoder.config[:lookup]
          lookup = Geocoder::Lookup.get(Geocoder.config[:lookup])
          if lookup && lookup.respond_to?(:cache) && lookup.cache
            lookup.cache.expire(:all)
            Rails.logger.debug("Geocoder cache expired using official method to prevent memory leak")
          end
        end
        
        # Also clear the configuration cache if it's a hash
        if Geocoder.config.cache.is_a?(Hash)
          Geocoder.config.cache.clear
          Rails.logger.debug("Geocoder configuration cache cleared to prevent memory leak")
        end
      end
      
      # Force garbage collection as additional safety measure
      GC.start
    rescue => e
      Rails.logger.warn("Failed to clear geocoder cache: #{e.message}")
      # Fall back to just forcing garbage collection
      GC.start
    end
  end

end

