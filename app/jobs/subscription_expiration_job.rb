# ABOUTME: Background job to process subscription expirations and cleanup referral codes
# ABOUTME: Downgrades expired premium users to free tier and expires old referral codes
class SubscriptionExpirationJob < ApplicationJob
  queue_as :default

  def perform
    # Simple expiration processing for premium users
    expired_count = User.tier_premium.where('subscription_expires_at < ?', Time.current).update_all(subscription_tier: :free)
    Rails.logger.info "Downgraded #{expired_count} expired premium users to free tier" if expired_count > 0
    
    # Expire old referral codes
    expired_codes_count = ReferralCode.active.where('expires_at < ?', Time.current).update_all(status: :expired)
    Rails.logger.info "Expired #{expired_codes_count} referral codes" if expired_codes_count > 0
  end
end
