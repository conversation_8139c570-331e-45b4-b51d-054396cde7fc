<% content_for :title, "Referral Code Management" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Referral Code Management</h1>
        <%= link_to "Subscriptions", admin_subscriptions_path, class: "btn btn-secondary" %>
      </div>

      <!-- Create New Code -->
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="mb-0">Create New Referral Code</h5>
        </div>
        <div class="card-body">
          <%= form_with model: [:admin, @new_code], local: true do |form| %>
            <% if @new_code.errors.any? %>
              <div class="alert alert-danger">
                <h6>Please fix the following errors:</h6>
                <ul class="mb-0">
                  <% @new_code.errors.full_messages.each do |message| %>
                    <li><%= message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <%= form.label :code, "Code" %>
                  <%= form.text_field :code, class: "form-control", placeholder: "e.g., WELCOME2024" %>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <%= form.label :tier_upgrade_to, "Upgrade To" %>
                  <%= form.select :tier_upgrade_to, 
                      options_for_select([
                        ['Premium', 'premium'],
                        ['Beta', 'beta']
                      ]), 
                      {}, 
                      { class: "form-control" } %>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <%= form.label :duration_months, "Duration (months)" %>
                  <%= form.number_field :duration_months, class: "form-control", value: 1, min: 1 %>
                </div>
              </div>
              <div class="col-md-2">
                <div class="form-group">
                  <%= form.label :max_uses, "Max Uses" %>
                  <%= form.number_field :max_uses, class: "form-control", value: 1, min: 1 %>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <%= form.label :expires_at, "Expires At" %>
                  <%= form.datetime_local_field :expires_at, 
                      class: "form-control",
                      value: 1.month.from_now.strftime("%Y-%m-%dT%H:%M") %>
                </div>
              </div>
            </div>
            <div class="form-group">
              <%= form.label :description, "Description" %>
              <%= form.text_area :description, class: "form-control", rows: 2, placeholder: "Optional description for this code" %>
            </div>
            <div class="form-group">
              <%= form.submit "Create Code", class: "btn btn-primary" %>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Existing Codes -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Existing Referral Codes</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Code</th>
                  <th>Status</th>
                  <th>Upgrade To</th>
                  <th>Duration</th>
                  <th>Usage</th>
                  <th>Expires</th>
                  <th>Created By</th>
                  <th>Description</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% @codes.each do |code| %>
                  <tr class="<%= 'table-warning' if code.expires_at&.past? %>">
                    <td>
                      <code class="font-weight-bold"><%= code.code %></code>
                    </td>
                    <td>
                      <span class="badge badge-<%= status_badge_class(code.status) %>">
                        <%= code.status.humanize %>
                      </span>
                    </td>
                    <td>
                      <span class="badge badge-<%= tier_badge_class(code.tier_upgrade_to) %>">
                        <%= code.tier_upgrade_to.humanize %>
                      </span>
                    </td>
                    <td>
                      <%= pluralize(code.duration_months, 'month') %>
                    </td>
                    <td>
                      <span class="<%= 'text-danger' if code.current_uses >= code.max_uses %>">
                        <%= code.current_uses %> / <%= code.max_uses %>
                      </span>
                    </td>
                    <td>
                      <% if code.expires_at %>
                        <%= code.expires_at.strftime("%b %d, %Y") %>
                        <% if code.expires_at.past? %>
                          <small class="text-danger">(Expired)</small>
                        <% end %>
                      <% else %>
                        <span class="text-muted">Never</span>
                      <% end %>
                    </td>
                    <td>
                      <%= code.created_by.email %>
                    </td>
                    <td>
                      <% if code.description.present? %>
                        <%= truncate(code.description, length: 50) %>
                      <% else %>
                        <span class="text-muted">No description</span>
                      <% end %>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <%= link_to "View", admin_referral_code_path(code), class: "btn btn-sm btn-outline-info" %>
                        <% if code.active? %>
                          <%= link_to "Disable", admin_referral_code_path(code), 
                              method: :patch, 
                              params: { referral_code: { status: 'disabled' } },
                              class: "btn btn-sm btn-outline-warning",
                              confirm: "Are you sure you want to disable this code?" %>
                        <% end %>
                        <%= link_to "Delete", admin_referral_code_path(code), 
                            method: :delete, 
                            class: "btn btn-sm btn-outline-danger",
                            confirm: "Are you sure you want to delete this code?" %>
                      </div>
                    </td>
                  </tr>
                <% end %>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <% if respond_to?(:paginate) && @codes.respond_to?(:current_page) %>
            <div class="d-flex justify-content-center">
              <%= paginate @codes %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>