<% content_for :title, "Subscription Management" %>

<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>Subscription Management</h1>
        <%= link_to "Referral Codes", admin_referral_codes_path, class: "btn btn-secondary" %>
      </div>

      <!-- Tier Statistics -->
      <div class="row mb-4">
        <div class="col-md-4">
          <div class="card bg-light">
            <div class="card-body text-center">
              <h3 class="text-muted"><%= @tier_stats[:free] %></h3>
              <p class="mb-0">Free Users</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-success text-white">
            <div class="card-body text-center">
              <h3><%= @tier_stats[:premium] %></h3>
              <p class="mb-0">Premium Users</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card bg-primary text-white">
            <div class="card-body text-center">
              <h3><%= @tier_stats[:beta] %></h3>
              <p class="mb-0">Beta Users</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Users Table -->
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">All Users</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Email</th>
                  <th>Name</th>
                  <th>Current Tier</th>
                  <th>Expires At</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% @users.each do |user| %>
                  <tr>
                    <td><%= user.email %></td>
                    <td>
                      <% if user.user_profile %>
                        <%= user.user_profile.first_name %> <%= user.user_profile.last_name %>
                      <% else %>
                        <em>No profile</em>
                      <% end %>
                    </td>
                    <td>
                      <span class="badge badge-<%= tier_badge_class(user.subscription_tier) %>">
                        <%= user.subscription_tier.humanize %>
                      </span>
                    </td>
                    <td>
                      <% if user.subscription_expires_at %>
                        <%= user.subscription_expires_at.strftime("%B %d, %Y") %>
                        <% if user.subscription_expires_at < Time.current %>
                          <small class="text-danger">(Expired)</small>
                        <% end %>
                      <% else %>
                        <span class="text-muted">Never</span>
                      <% end %>
                    </td>
                    <td>
                      <% if user.active_subscription? %>
                        <span class="badge badge-success">Active</span>
                      <% else %>
                        <span class="badge badge-secondary">Inactive</span>
                      <% end %>
                    </td>
                    <td>
                      <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" data-toggle="modal" data-target="#updateTierModal<%= user.id %>">
                          Update Tier
                        </button>
                      </div>
                    </td>
                  </tr>

                  <!-- Update Tier Modal -->
                  <div class="modal fade" id="updateTierModal<%= user.id %>" tabindex="-1">
                    <div class="modal-dialog">
                      <div class="modal-content">
                        <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
                          <div class="modal-header">
                            <h5 class="modal-title">Update Tier for <%= user.email %></h5>
                            <button type="button" class="close" data-dismiss="modal">
                              <span>&times;</span>
                            </button>
                          </div>
                          <div class="modal-body">
                            <div class="form-group">
                              <%= form.label :subscription_tier, "Subscription Tier" %>
                              <%= form.select :subscription_tier, 
                                  options_for_select([
                                    ['Free', 'free'],
                                    ['Premium', 'premium'],
                                    ['Beta', 'beta']
                                  ], user.subscription_tier), 
                                  {}, 
                                  { class: "form-control" } %>
                            </div>
                            <div class="form-group">
                              <%= form.label :subscription_expires_at, "Expires At (leave blank for Beta/permanent)" %>
                              <%= form.datetime_local_field :subscription_expires_at, 
                                  value: user.subscription_expires_at&.strftime("%Y-%m-%dT%H:%M"),
                                  class: "form-control" %>
                              <small class="form-text text-muted">
                                Only applies to Premium tier. Beta users never expire.
                              </small>
                            </div>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <%= form.submit "Update Tier", class: "btn btn-primary" %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <% if respond_to?(:paginate) && @users.respond_to?(:current_page) %>
            <div class="d-flex justify-content-center">
              <%= paginate @users %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<% content_for :page_scripts do %>
<script>
// Auto-clear expiration date when selecting Free or Beta tier
document.querySelectorAll('select[name="user[subscription_tier]"]').forEach(function(select) {
  select.addEventListener('change', function() {
    const expiresField = this.closest('.modal-content').querySelector('input[name="user[subscription_expires_at]"]');
    if (this.value === 'free' || this.value === 'beta') {
      expiresField.value = '';
      expiresField.disabled = true;
    } else {
      expiresField.disabled = false;
    }
  });
});
</script>
<% end %>