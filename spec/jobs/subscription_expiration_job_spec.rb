require 'rails_helper'

RSpec.describe SubscriptionExpirationJob, type: :job do
  describe "#perform" do
    let(:admin_user) { create(:user, role: :super_boss) }
    
    it "downgrades expired premium users to free tier" do
      expired_user = create(:user, subscription_tier: :premium, subscription_expires_at: 1.day.ago)
      active_user = create(:user, subscription_tier: :premium, subscription_expires_at: 1.month.from_now)
      
      described_class.new.perform
      
      expect(expired_user.reload.subscription_tier).to eq("free")
      expect(active_user.reload.subscription_tier).to eq("premium")
    end
    
    it "expires old referral codes" do
      expired_code = create(:referral_code, status: :active, expires_at: 1.day.ago, created_by: admin_user)
      active_code = create(:referral_code, status: :active, expires_at: 1.month.from_now, created_by: admin_user)
      
      described_class.new.perform
      
      expect(expired_code.reload.status).to eq("expired")
      expect(active_code.reload.status).to eq("active")
    end
    
    it "does not affect beta users" do
      beta_user = create(:user, subscription_tier: :beta, subscription_expires_at: 1.day.ago)
      
      described_class.new.perform
      
      expect(beta_user.reload.subscription_tier).to eq("beta")
    end
  end
end
